#!/usr/bin/env python3
"""
Test script for wordlist extraction from commands
"""

from ipcrawler.loading import extract_wordlist_from_command

def test_wordlist_extraction():
    """Test the wordlist extraction function"""
    
    test_commands = [
        # ffuf commands
        "ffuf -u http://example.com/FUZZ -w /usr/share/seclists/Discovery/Web-Content/common.txt",
        "ffuf -u http://example.com/FUZZ -w /usr/share/seclists/Discovery/Web-Content/directory-list-2.3-medium.txt -t 50",
        
        # gobuster commands  
        "gobuster dir -u http://example.com -w /usr/share/wordlists/dirb/common.txt",
        "gobuster dir -u http://example.com --wordlist=/usr/share/seclists/Discovery/Web-Content/big.txt",
        "gobuster dir -u http://example.com --wordlist /usr/share/seclists/Discovery/Web-Content/raft-medium-directories.txt",
        
        # feroxbuster commands
        "feroxbuster -u http://example.com -w /usr/share/seclists/Discovery/Web-Content/common.txt",
        
        # wfuzz commands
        "wfuzz -w /usr/share/seclists/Discovery/DNS/subdomains-top1million-5000.txt -u http://FUZZ.example.com",
        
        # dirsearch commands
        "dirsearch -u http://example.com -w /usr/share/seclists/Discovery/Web-Content/big.txt",
        
        # Commands without wordlists
        "nmap -sV -sC example.com",
        "whatweb http://example.com",
        
        # Edge cases
        "ffuf -u http://example.com/FUZZ -w common.txt",  # No path
        "gobuster dir -u http://example.com -w /path/with spaces/wordlist.txt",  # Spaces in path
    ]
    
    print("🧪 Testing wordlist extraction from commands...\n")
    
    for i, command in enumerate(test_commands, 1):
        result = extract_wordlist_from_command(command)
        print(f"Test {i:2d}: {result or 'None'}")
        print(f"         Command: {command}")
        print()
    
    print("✅ Wordlist extraction tests completed!")

if __name__ == "__main__":
    test_wordlist_extraction()
