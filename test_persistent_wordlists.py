#!/usr/bin/env python3
"""
Test script for persistent wordlist functionality
"""

import asyncio
import time
import os
import shutil
from ipcrawler.loading import ScanUI

async def test_persistent_wordlists():
    """Test that wordlists and completed scans persist correctly"""
    print("🧪 Testing persistent wordlist and completed scan functionality...")
    
    target = 'test-persistence.htb'
    
    # Clean up any existing cache for this test
    cache_dir = f"results/{target}/.ui_cache"
    if os.path.exists(cache_dir):
        shutil.rmtree(cache_dir)
    
    print("\n📝 Phase 1: Creating initial scan session...")
    
    # Create first UI session
    ui1 = ScanUI(target)
    ui1.start_ui()
    
    try:
        # Add tools with wordlists
        ui1.start_tool('Feroxbuster', 'common.txt (2,048 lines)')
        await asyncio.sleep(0.5)
        
        ui1.start_tool('Gobuster', 'directory-list-2.3-medium.txt (220,560 lines)')
        await asyncio.sleep(0.5)
        
        ui1.start_tool('FFuf', 'raft-medium-directories.txt (30,000 lines)')
        await asyncio.sleep(1)
        
        # Complete some tools
        ui1.complete_tool('Feroxbuster', 'Found 15 directories')
        await asyncio.sleep(0.5)
        
        ui1.complete_tool('Gobuster', 'Found 42 directories')
        await asyncio.sleep(0.5)
        
        # Error one tool
        ui1.error_tool('FFuf', 'Connection timeout')
        await asyncio.sleep(1)
        
        print("✅ Phase 1 completed - tools added and completed")
        
    finally:
        ui1.stop_ui(show_completion=False)
    
    print("\n📝 Phase 2: Creating new session to test persistence...")
    
    # Create second UI session to test persistence
    ui2 = ScanUI(target)
    ui2.start_ui()
    
    try:
        # Check that completed tools are loaded
        print(f"📊 Loaded {len(ui2.persistent_data.completed_tools)} completed tools from cache")
        print(f"📚 Loaded {len(ui2.persistent_data.wordlist_history)} wordlist history entries")
        
        # Add new tools to test mixed state
        ui2.start_tool('Wfuzz', 'subdomains-top1million-5000.txt (4,989 lines)')
        await asyncio.sleep(0.5)
        
        ui2.start_tool('Dirsearch', 'big.txt (20,469 lines)')
        await asyncio.sleep(1)
        
        # Complete new tools
        ui2.complete_tool('Wfuzz', 'Found 3 subdomains')
        await asyncio.sleep(0.5)
        
        ui2.complete_tool('Dirsearch', 'Found 8 files')
        await asyncio.sleep(2)
        
        print("✅ Phase 2 completed - persistence verified and new tools added")
        
    finally:
        ui2.stop_ui(show_completion=False)
    
    print("\n📝 Phase 3: Final verification...")
    
    # Create third UI session for final verification
    ui3 = ScanUI(target)
    ui3.start_ui()
    
    try:
        await asyncio.sleep(1)
        
        print(f"📊 Final count: {len(ui3.persistent_data.completed_tools)} completed tools")
        print(f"📚 Final count: {len(ui3.persistent_data.wordlist_history)} wordlist history entries")
        
        # Verify specific tools exist
        expected_tools = ['Feroxbuster', 'Gobuster', 'FFuf', 'Wfuzz', 'Dirsearch']
        for tool in expected_tools:
            if tool in ui3.persistent_data.completed_tools:
                print(f"✅ {tool} found in completed tools")
            else:
                print(f"❌ {tool} missing from completed tools")
        
        await asyncio.sleep(2)
        
    finally:
        ui3.stop_ui(show_completion=False)
    
    print("\n🎉 Persistence test completed!")
    print(f"📁 Cache stored in: {cache_dir}")

if __name__ == "__main__":
    asyncio.run(test_persistent_wordlists())
