#!/usr/bin/env python3

import sys
import os
import time
import asyncio

# Add the ipcrawler directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from ipcrawler.loading import (
    PersistentScanData, ServiceDiscovery, PatternDiscovery, 
    ScanUI, start_scan_ui, complete_tool_scan, ToolStatus
)

def test_persistent_data():
    """Test the persistent data functionality"""
    print("🧪 Testing Persistent Scan Data...")
    
    # Test target
    target = "test.example.com"
    
    # Create persistent data instance
    persistent_data = PersistentScanData(target)
    
    # Add some test services
    test_services = [
        ServiceDiscovery(port=22, protocol="tcp", service="ssh", version="OpenSSH 8.0"),
        ServiceDiscovery(port=80, protocol="tcp", service="http", version="Apache 2.4"),
        ServiceDiscovery(port=443, protocol="tcp", service="https", version="Apache 2.4")
    ]
    
    persistent_data.discovered_services = test_services
    
    # Add some test patterns
    test_patterns = [
        PatternDiscovery(type="vulnerability", description="SQL Injection found", source="sqlmap", severity="high"),
        PatternDiscovery(type="pattern", description="Admin panel discovered", source="dirb", severity="medium"),
        PatternDiscovery(type="credential", description="Default credentials found", source="hydra", severity="critical")
    ]
    
    persistent_data.discovered_patterns = test_patterns
    
    # Save to cache
    persistent_data._save_to_cache()
    
    print(f"✅ Saved {len(test_services)} services and {len(test_patterns)} patterns to cache")
    
    # Create new instance to test loading
    persistent_data2 = PersistentScanData(target)
    
    print(f"✅ Loaded {len(persistent_data2.discovered_services)} services and {len(persistent_data2.discovered_patterns)} patterns from cache")
    
    # Verify data integrity
    assert len(persistent_data2.discovered_services) == len(test_services)
    assert len(persistent_data2.discovered_patterns) == len(test_patterns)
    
    print("✅ Persistent data test passed!")
    return True

def test_ui_functionality():
    """Test the UI functionality with persistent data"""
    print("\n🧪 Testing UI Functionality...")
    
    target = "test.example.com"
    
    # Create scan UI
    scan_ui = ScanUI(target)
    
    # Test adding tools
    scan_ui.add_tool("nmap")
    scan_ui.add_tool("dirb")
    
    # Test starting tools
    scan_ui.start_tool("nmap")
    time.sleep(0.1)  # Small delay to simulate scan time
    
    # Test completing tools
    scan_ui.complete_tool("nmap", "Found 3 open ports")
    
    # Check if data was saved to persistent storage
    assert "nmap" in scan_ui.persistent_data.completed_tools
    assert scan_ui.persistent_data.completed_tools["nmap"].status == ToolStatus.COMPLETED
    
    print("✅ UI functionality test passed!")
    return True

def test_panel_creation():
    """Test the new panel creation methods"""
    print("\n🧪 Testing Panel Creation...")
    
    target = "test.example.com"
    scan_ui = ScanUI(target)
    
    # Add some test data
    scan_ui.persistent_data.discovered_services = [
        ServiceDiscovery(port=22, protocol="tcp", service="ssh", version="OpenSSH 8.0"),
        ServiceDiscovery(port=80, protocol="tcp", service="http", version="Apache 2.4")
    ]
    
    scan_ui.persistent_data.discovered_patterns = [
        PatternDiscovery(type="vulnerability", description="XSS found", source="xsser", severity="medium")
    ]
    
    # Test panel creation
    try:
        open_ports_panel = scan_ui._create_open_ports_panel()
        discoveries_panel = scan_ui._create_discoveries_panel()
        print("✅ Panel creation test passed!")
        return True
    except Exception as e:
        print(f"❌ Panel creation test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting IPCrawler UI Tests...\n")
    
    tests = [
        test_persistent_data,
        test_ui_functionality,
        test_panel_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The persistent UI functionality is working correctly.")
        return 0
    else:
        print("⚠️ Some tests failed. Check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
