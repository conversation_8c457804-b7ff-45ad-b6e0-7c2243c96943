import time
import asyncio
import json
import os
from datetime import datetime
from typing import Dict, Optional, List
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

from rich.console import Console, RenderableType
from rich.live import Live
from rich.panel import Panel
from rich.text import Text
from rich import box
from rich.layout import Layout
from rich.table import Table
from rich.spinner import Spinner
from rich.columns import Columns
from rich.align import Align
from ipcrawler.config import config

console = Console()

class ToolStatus(Enum):
    PENDING = "pending"
    RUNNING = "running" 
    COMPLETED = "completed"
    ERROR = "error"

@dataclass
class ToolInfo:
    name: str
    status: ToolStatus = ToolStatus.PENDING
    summary: Optional[str] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    wordlist_info: Optional[str] = None

    def duration(self) -> str:
        """Calculate and format duration"""
        if self.start_time and self.end_time:
            duration = int(self.end_time - self.start_time)
            if duration < 60:
                return f"{duration}s"
            else:
                minutes = duration // 60
                seconds = duration % 60
                return f"{minutes}m {seconds}s"
        return "—"

@dataclass
class ServiceDiscovery:
    """Represents a discovered service/port"""
    port: int
    protocol: str
    service: str
    state: str = "open"
    version: str = ""

@dataclass
class PatternDiscovery:
    """Represents a discovered pattern/vulnerability/finding"""
    type: str  # "pattern", "vulnerability", "credential", etc.
    description: str
    source: str  # which tool found it
    severity: str = "info"  # "critical", "high", "medium", "low", "info"

class SessionScanData:
    """Manages scan data for current session only - no cross-scan persistence"""

    def __init__(self, target: str):
        self.target = target

        # Session-only data (not persistent across different scans)
        self.completed_tools: Dict[str, ToolInfo] = {}
        self.discovered_services: List[ServiceDiscovery] = []
        self.discovered_patterns: List[PatternDiscovery] = []
        self.scan_start_time: Optional[float] = None
        self.scan_end_time: Optional[float] = None

        # Session wordlist tracking
        self.used_wordlists: Dict[str, str] = {}  # tool_name -> wordlist_info
        self.wordlist_history: List[Dict] = []  # History of all wordlists used in this session

    def add_completed_tool(self, tool_name: str, tool_info: ToolInfo):
        """Add a completed tool to session data"""
        self.completed_tools[tool_name] = tool_info

    def update_scan_timing(self, start_time: Optional[float] = None, end_time: Optional[float] = None):
        """Update scan timing for session"""
        if start_time is not None:
            self.scan_start_time = start_time
        if end_time is not None:
            self.scan_end_time = end_time

    def add_wordlist_usage(self, tool_name: str, wordlist_info: str):
        """Add wordlist usage to session"""
        import time
        self.used_wordlists[tool_name] = wordlist_info

        # Add to history with timestamp
        history_entry = {
            'tool': tool_name,
            'wordlist': wordlist_info,
            'timestamp': time.time()
        }
        self.wordlist_history.append(history_entry)

        # Keep only last 50 entries to prevent unlimited growth
        if len(self.wordlist_history) > 50:
            self.wordlist_history = self.wordlist_history[-50:]

    def remove_wordlist_usage(self, tool_name: str):
        """Remove wordlist usage when tool completes"""
        self.used_wordlists.pop(tool_name, None)

    def refresh_discoveries(self):
        """Refresh discoveries from scan results"""
        # Parse current scan results to update discoveries
        self._parse_scan_results()
        self._save_to_cache()

    def _parse_scan_results(self):
        """Parse scan results to extract services and patterns"""
        try:
            from ipcrawler.consolidator import ResultConsolidator
            consolidator = ResultConsolidator("results")
            target_results = consolidator.parse_target_results(self.target)

            # Update discovered services
            self.discovered_services = []
            for service in target_results.open_ports:
                self.discovered_services.append(ServiceDiscovery(
                    port=service.port,
                    protocol=service.protocol,
                    service=service.service,
                    state=service.state,
                    version=service.version
                ))

            # Update discovered patterns
            self.discovered_patterns = []
            for pattern in target_results.patterns:
                self.discovered_patterns.append(PatternDiscovery(
                    type="pattern",
                    description=pattern,
                    source="scan",
                    severity="info"
                ))

            for vuln in target_results.vulnerabilities:
                self.discovered_patterns.append(PatternDiscovery(
                    type="vulnerability",
                    description=vuln,
                    source="scan",
                    severity="high"
                ))

        except Exception as e:
            # Silently handle parsing errors
            pass

class LiveScanLayout:
    """A renderable that automatically updates the scan layout"""

    def __init__(self, scan_ui):
        self.scan_ui = scan_ui

    def __rich__(self) -> RenderableType:
        """Return the current layout"""
        return self.scan_ui._create_layout()

class ScanUI:
    """Full-screen TUI application for IPCrawler scans"""

    def __init__(self, target: str):
        self.target = target
        self.scan_started = False
        self.scan_start_time = None
        self.tools: Dict[str, ToolInfo] = {}
        self.console = Console()
        self.live = None
        self._is_active = False
        self.layout = None
        self.running_tools = {}
        self.completed_tools = {}
        self.spinner = Spinner("dots", style="bold yellow")
        self.spinner_frame = 0

        # Track wordlists being used
        self.active_wordlists: Dict[str, str] = {}  # tool_name -> wordlist_info

        # Initialize session data storage (no cross-scan persistence)
        self.session_data = SessionScanData(target)
        
    def set_start_time(self, start_time: float):
        """Update the scan start time and mark as started"""
        self.scan_start_time = start_time
        self.scan_started = True

        # Save to persistent storage
        self.persistent_data.update_scan_timing(start_time=start_time)

        self._update_display()
        
    def add_tool(self, tool_name: str):
        """Register a new tool that will be run"""
        self.tools[tool_name] = ToolInfo(name=tool_name)
        self._update_display()
        
    def start_tool(self, tool_name: str, wordlist_info: Optional[str] = None):
        """Mark a tool as started/running"""
        display_name = tool_name
        if wordlist_info:
            display_name = f"{tool_name} using {wordlist_info}"
            # Track the wordlist being used
            self.active_wordlists[tool_name] = wordlist_info
            # Add to session storage
            self.session_data.add_wordlist_usage(tool_name, wordlist_info)

        # Check if tool already exists and preserve start time if it does
        existing_tool = self.tools.get(tool_name)
        start_time = existing_tool.start_time if existing_tool and existing_tool.start_time else time.time()

        tool_info = ToolInfo(
            name=display_name,
            status=ToolStatus.RUNNING,
            start_time=start_time,
            wordlist_info=wordlist_info
        )

        self.tools[tool_name] = tool_info
        self.running_tools[tool_name] = tool_info
        # Remove from completed if it was there (for re-runs)
        self.completed_tools.pop(tool_name, None)
        self._update_display()
            
    def complete_tool(self, tool_name: str, summary: Optional[str] = None):
        """Mark a tool as completed with optional summary"""
        if tool_name in self.tools:
            self.tools[tool_name].status = ToolStatus.COMPLETED
            self.tools[tool_name].summary = summary
            # Ensure end_time is set if not already
            if not self.tools[tool_name].end_time:
                self.tools[tool_name].end_time = time.time()

            # Move from running to completed
            self.completed_tools[tool_name] = self.tools[tool_name]
            self.running_tools.pop(tool_name, None)

            # Remove from active wordlists when tool completes
            self.active_wordlists.pop(tool_name, None)
            # Remove from session wordlist tracking
            self.session_data.remove_wordlist_usage(tool_name)

            # Save to session storage
            self.session_data.add_completed_tool(tool_name, self.tools[tool_name])

            self._update_display()

    def error_tool(self, tool_name: str, error_msg: Optional[str] = None):
        """Mark a tool as errored"""
        if tool_name in self.tools:
            self.tools[tool_name].status = ToolStatus.ERROR
            self.tools[tool_name].summary = error_msg or "Error"
            # Ensure end_time is set if not already
            if not self.tools[tool_name].end_time:
                self.tools[tool_name].end_time = time.time()

            # Move from running to completed
            self.completed_tools[tool_name] = self.tools[tool_name]
            self.running_tools.pop(tool_name, None)

            # Remove from active wordlists when tool errors
            self.active_wordlists.pop(tool_name, None)
            # Remove from session wordlist tracking
            self.session_data.remove_wordlist_usage(tool_name)

            # Save to session storage
            self.session_data.add_completed_tool(tool_name, self.tools[tool_name])

            self._update_display()

    def get_current_wordlists(self) -> List[str]:
        """Get list of currently active wordlists"""
        wordlists = []
        for tool_name, wordlist_info in self.active_wordlists.items():
            if wordlist_info:
                # Extract just the filename from the wordlist info
                if "/" in wordlist_info:
                    filename = wordlist_info.split("/")[-1]
                else:
                    filename = wordlist_info
                wordlists.append(f"{tool_name}: {filename}")
        return wordlists

    def _format_elapsed_time(self) -> str:
        """Format elapsed time as HH:MM:SS"""
        if not self.scan_started or not self.scan_start_time:
            return "00:00:00"
            
        elapsed = int(time.time() - self.scan_start_time)
        hours = elapsed // 3600
        minutes = (elapsed % 3600) // 60
        seconds = elapsed % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    
    def _get_status_icon(self, status: ToolStatus) -> str:
        """Get the icon for tool status"""
        icons = {
            ToolStatus.PENDING: "[ ]",
            ToolStatus.RUNNING: "[→]", 
            ToolStatus.COMPLETED: "[✓]",
            ToolStatus.ERROR: "[!]"
        }
        return icons[status]
    
    def _get_status_color(self, status: ToolStatus) -> str:
        """Get the color for tool status"""
        colors = {
            ToolStatus.PENDING: "dim white",
            ToolStatus.RUNNING: "yellow",
            ToolStatus.COMPLETED: "green", 
            ToolStatus.ERROR: "red"
        }
        return colors[status]
        
    def _create_header(self) -> Panel:
        """Create the header panel"""
        elapsed_time = self._format_elapsed_time()

        # Create a more elegant header layout
        header_content = Table.grid(padding=1)
        header_content.add_column(justify="left", ratio=1)
        header_content.add_column(justify="center", ratio=2)
        header_content.add_column(justify="right", ratio=1)

        # Status indicator with animated spinner
        if self.running_tools:
            # Get current spinner frame
            spinner_frames = ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]
            current_frame = spinner_frames[self.spinner_frame % len(spinner_frames)]
            self.spinner_frame += 1

            status_text = Text()
            status_text.append(current_frame, style="bold green")
            status_text.append(" SCANNING", style="bold green")
        else:
            status_text = Text()
            status_text.append("○", style="dim white")
            status_text.append(" WAITING", style="dim white")

        # Create target display with better formatting
        target_display = Text()
        target_display.append("🎯 ", style="bold yellow")
        target_display.append(self.target, style="bold cyan")

        # Create time display with wordlist info
        time_display = Text()
        time_display.append("⏱️  ", style="bold blue")
        time_display.append(elapsed_time, style="bold white")

        # Add wordlist info to time display if any wordlists are active
        current_wordlists = self.get_current_wordlists()
        if current_wordlists:
            time_display.append("\n📚 ", style="bold green")
            if len(current_wordlists) == 1:
                time_display.append(current_wordlists[0], style="green")
            else:
                time_display.append(f"{len(current_wordlists)} wordlists active", style="green")

        # Create stats display
        stats_display = Text()
        stats_display.append("📊 ", style="bold magenta")
        stats_display.append(f"Active: {len(self.running_tools)}", style="yellow")
        stats_display.append(" | ", style="dim white")
        stats_display.append(f"Done: {len(self.completed_tools)}", style="green")

        header_content.add_row(
            target_display,
            "[bold white]🕷️  IPCrawler Security Scanner[/bold white]",
            time_display
        )

        header_content.add_row(
            status_text,
            "",
            stats_display
        )

        return Panel(
            header_content,
            title="[bold cyan]🔍 IPCrawler Live Dashboard[/bold cyan]",
            border_style="cyan",
            box=box.HEAVY
        )
    
    def _create_running_panel(self) -> Panel:
        """Create the running tools panel"""
        if not self.running_tools:
            content = Align.center(
                Text("🔍 No active scans\n⏳ Waiting for tools to start...",
                     style="dim white", justify="center"),
                vertical="middle"
            )
        else:
            table = Table(show_header=False, box=None, padding=(0, 1))
            table.add_column("Status", width=4)
            table.add_column("Tool", style="white")

            # Use different spinner frames for variety
            spinner_frames = ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]

            for i, (tool_name, tool_info) in enumerate(self.running_tools.items()):
                # Use different spinner frame for each tool to create wave effect
                frame_index = (self.spinner_frame + i * 2) % len(spinner_frames)
                current_frame = spinner_frames[frame_index]

                spinner_text = Text()
                spinner_text.append(current_frame, style="bold yellow")

                table.add_row(
                    spinner_text,
                    tool_info.name
                )
            content = table

        return Panel(
            content,
            title=f"[bold yellow]🔄 Active Scans ({len(self.running_tools)})[/bold yellow]",
            border_style="yellow",
            box=box.ROUNDED
        )

    def _create_completed_panel(self) -> Panel:
        """Create the completed tools panel - shows only tool names, no discovered output"""
        # Use session data for current scan session only
        all_completed = self.session_data.completed_tools.copy()
        # Also include any completed tools from current session that might not be saved yet
        all_completed.update(self.completed_tools)

        if not all_completed:
            content = Align.center(
                Text("✅ No completed scans\n⏳ Waiting for tools to finish...",
                     style="dim white", justify="center"),
                vertical="middle"
            )
        else:
            # Create table for completed tools - ONLY show tool names and status
            table = Table(show_header=False, box=None, padding=(0, 1))
            table.add_column("Tool", ratio=1)

            # Sort by completion time (most recent first)
            sorted_tools = sorted(all_completed.items(),
                                key=lambda x: x[1].end_time or 0, reverse=True)

            for tool_name, tool_info in sorted_tools:
                icon = self._get_status_icon(tool_info.status)
                color = self._get_status_color(tool_info.status)

                # Show ONLY tool name and duration - NO discovered output/summary
                tool_display = f"{icon} {tool_name}"
                if tool_info.duration() != "—":
                    tool_display += f" ({tool_info.duration()})"

                table.add_row(
                    Text(tool_display, style=color)
                )
            content = table

        return Panel(
            content,
            title=f"[bold blue]✅ Completed Scans ({len(all_completed)})[/bold blue]",
            border_style="blue",
            box=box.ROUNDED
        )
    
    def _create_open_ports_panel(self) -> Panel:
        """Create the open ports panel showing discovered services"""
        # Refresh discoveries to get latest data
        self.persistent_data.refresh_discoveries()

        if not self.persistent_data.discovered_services:
            content = Align.center(
                Text("🔍 No open ports discovered yet\n⏳ Port scans will appear here...",
                     style="dim white", justify="center"),
                vertical="middle"
            )
        else:
            table = Table(show_header=False, box=None, padding=(0, 1))
            table.add_column("Port", width=8, style="cyan")
            table.add_column("Service", style="white")
            table.add_column("Version", style="dim white")

            # Sort services by port number
            sorted_services = sorted(self.persistent_data.discovered_services, key=lambda s: s.port)

            for service in sorted_services:
                port_str = f"{service.port}/{service.protocol}"
                service_name = service.service if service.service != "unknown" else "?"
                version_str = service.version[:30] + "..." if len(service.version) > 30 else service.version

                table.add_row(
                    port_str,
                    service_name,
                    version_str
                )
            content = table

        return Panel(
            content,
            title="[bold cyan]🔓 Open Ports[/bold cyan]",
            border_style="cyan",
            box=box.ROUNDED
        )

    def _create_discoveries_panel(self) -> Panel:
        """Create the discoveries panel showing patterns and vulnerabilities"""
        if not self.persistent_data.discovered_patterns:
            content = Align.center(
                Text("🔍 No discoveries yet\n🕵️ Findings will appear here...",
                     style="dim white", justify="center"),
                vertical="middle"
            )
        else:
            table = Table(show_header=False, box=None, padding=(0, 1))
            table.add_column("Type", width=8)
            table.add_column("Finding", style="white")

            # Group by severity and type
            critical_patterns = [p for p in self.persistent_data.discovered_patterns if p.severity == "critical"]
            high_patterns = [p for p in self.persistent_data.discovered_patterns if p.severity == "high"]
            other_patterns = [p for p in self.persistent_data.discovered_patterns if p.severity not in ["critical", "high"]]

            # Show critical first, then high, then others
            for pattern in critical_patterns + high_patterns + other_patterns[:10]:  # Limit to prevent overflow
                if pattern.severity == "critical":
                    type_style = "bold red"
                    icon = "🚨"
                elif pattern.severity == "high":
                    type_style = "red"
                    icon = "⚠️"
                elif pattern.type == "vulnerability":
                    type_style = "yellow"
                    icon = "🔓"
                else:
                    type_style = "dim white"
                    icon = "📋"

                # Truncate long descriptions
                desc = pattern.description[:50] + "..." if len(pattern.description) > 50 else pattern.description

                table.add_row(
                    Text(f"{icon} {pattern.type.title()}", style=type_style),
                    desc
                )
            content = table

        return Panel(
            content,
            title="[bold yellow]🔍 Discoveries[/bold yellow]",
            border_style="yellow",
            box=box.ROUNDED
        )

    def _create_wordlists_discoveries_panel(self) -> Panel:
        """Create the wordlists panel - always shows wordlists (active + history), persistent"""
        table = Table(show_header=False, box=None, padding=(0, 1))
        table.add_column("Status", width=8)
        table.add_column("Tool", width=12)
        table.add_column("Wordlist", style="white")

        # Show active wordlists first
        current_wordlists = self.get_current_wordlists()
        active_count = 0

        for wordlist_entry in current_wordlists:
            if ":" in wordlist_entry:
                tool, wordlist = wordlist_entry.split(":", 1)
                table.add_row(
                    Text("🟢 Active", style="bold green"),
                    Text(tool.strip(), style="bold green"),
                    Text(wordlist.strip(), style="green")
                )
                active_count += 1

        # Show recent wordlist history (excluding currently active ones)
        history_count = 0
        max_history = 10 - active_count  # Adjust based on active wordlists

        if max_history > 0 and self.session_data.wordlist_history:
            # Get recent history, excluding currently active tools
            active_tools = {entry.split(":")[0].strip() for entry in current_wordlists if ":" in entry}

            for entry in reversed(self.session_data.wordlist_history[-max_history:]):
                if entry['tool'] not in active_tools and history_count < max_history:
                    table.add_row(
                        Text("📚 Used", style="dim cyan"),
                        Text(entry['tool'], style="cyan"),
                        Text(entry['wordlist'], style="dim white")
                    )
                    history_count += 1

        # If no wordlists at all, show helpful message
        if active_count == 0 and history_count == 0:
            content = Align.center(
                Text("📚 No wordlists used yet\n🔍 Wordlists will appear here when tools start...",
                     style="dim white", justify="center"),
                vertical="middle"
            )
        else:
            content = table

        return Panel(
            content,
            title=f"[bold magenta]📚 Wordlists ({active_count} active, {history_count} recent)[/bold magenta]",
            border_style="magenta",
            box=box.ROUNDED
        )
    
    def _create_layout(self):
        """Create the full-screen TUI layout"""
        layout = Layout()

        # Split into header and body
        layout.split_column(
            Layout(name="header", size=6),
            Layout(name="body")
        )

        # Split body into three columns: active scans, completed scans, wordlists/discoveries
        layout["body"].split_row(
            Layout(name="active_scans", ratio=1),
            Layout(name="completed_scans", ratio=1),
            Layout(name="wordlists_discoveries", ratio=1)
        )

        # Populate the layout
        layout["header"].update(self._create_header())
        layout["active_scans"].update(self._create_running_panel())
        layout["completed_scans"].update(self._create_completed_panel())
        layout["wordlists_discoveries"].update(self._create_wordlists_discoveries_panel())

        return layout
    
    def start_ui(self):
        """Start the full-screen TUI"""
        if not self._is_active:
            # Create a live renderable that will automatically update
            live_layout = LiveScanLayout(self)

            # Start live display with full screen refresh
            self.live = Live(
                live_layout,
                console=self.console,
                refresh_per_second=4,  # Higher refresh rate for smooth animations and time updates
                auto_refresh=True,  # Auto refresh will now work with our custom renderable
                screen=True,  # Take over the entire screen
                transient=False
            )
            self.live.start()
            self._is_active = True
    
    def _update_display(self):
        """Update the full-screen TUI layout"""
        if self.live and self._is_active and self.layout:
            try:
                # Update each section of the layout
                self.layout["header"].update(self._create_header())
                self.layout["active_scans"].update(self._create_running_panel())
                self.layout["completed_scans"].update(self._create_completed_panel())
                self.layout["wordlists_discoveries"].update(self._create_wordlists_discoveries_panel())
            except Exception:
                # If update fails, continue silently
                pass
            
    def stop_ui(self, show_completion: bool = True):
        """Stop the full-screen TUI"""
        if self.live and self._is_active:
            self._is_active = False
            self.live.stop()
            self.live = None
            self.layout = None
            
            if show_completion:
                # Clear screen and show completion message
                self.console.clear()
                elapsed_str = self._format_elapsed_time()
                completed_tools = len(self.completed_tools)
                error_tools = sum(1 for t in self.completed_tools.values() if t.status == ToolStatus.ERROR)

                # Use the standardized completion message
                show_completion_message(
                    target=self.target,
                    duration=elapsed_str,
                    completed_tools=completed_tools,
                    error_tools=error_tools,
                    canceled=False,
                    timed_out=False
                )

# Global scan UI instance
scan_ui = None

def start_scan_ui(target: str):
    """Start the scan UI for a target"""
    global scan_ui
    if scan_ui is None:
        scan_ui = ScanUI(target)
        scan_ui.start_ui()

def stop_scan_ui(show_completion: bool = True):
    """Stop the scan UI - session ends"""
    global scan_ui
    if scan_ui:
        # Update final scan end time
        scan_ui.session_data.update_scan_timing(end_time=time.time())
        scan_ui.stop_ui(show_completion)
        # Reset scan_ui for next scan session
        scan_ui = None

def extract_wordlist_from_command(command: str) -> Optional[str]:
    """Extract wordlist information from command string"""
    import re

    # Common wordlist patterns
    patterns = [
        r'-w\s+([^\s]+)',  # -w wordlist.txt
        r'--wordlist[=\s]+([^\s]+)',  # --wordlist=wordlist.txt or --wordlist wordlist.txt
        r'-W\s+([^\s]+)',  # -W wordlist.txt (some tools)
        r'--dict[=\s]+([^\s]+)',  # --dict=wordlist.txt
        r'-d\s+([^\s]+)',  # -d wordlist.txt (some tools)
    ]

    for pattern in patterns:
        match = re.search(pattern, command)
        if match:
            wordlist_path = match.group(1)
            # Extract just the filename
            if '/' in wordlist_path:
                return wordlist_path.split('/')[-1]
            return wordlist_path

    return None

def start_tool_loading(tool_name: str, target: str, wordlist_info: Optional[str] = None, command: Optional[str] = None):
    """Start loading for a tool"""
    global scan_ui
    if scan_ui is None:
        start_scan_ui(target)
    if scan_ui:
        # If no wordlist_info provided but command is available, try to extract it
        if not wordlist_info and command:
            wordlist_info = extract_wordlist_from_command(command)
        scan_ui.start_tool(tool_name, wordlist_info)

def stop_tool_loading():
    """Stop loading for a tool"""
    global scan_ui
    # Tool completion is handled by complete_tool_scan or error_tool_scan
    pass

def complete_tool_scan(tool_name: str, summary: Optional[str] = None):
    """Mark a tool as completed"""
    global scan_ui
    if scan_ui:
        scan_ui.complete_tool(tool_name, summary)

def error_tool_scan(tool_name: str, error_msg: Optional[str] = None):
    """Mark a tool as errored"""
    global scan_ui
    if scan_ui:
        scan_ui.error_tool(tool_name, error_msg)

def record_tool_activity(activity_type: str = "output"):
    """Record tool activity - no longer needed with new UI"""
    pass

def update_tool_progress(percentage: Optional[int] = None, status: str = ""):
    """Update tool progress - no longer needed with new UI"""
    pass

class ScanStatus:
    """Clean status display for scan operations"""

    @staticmethod
    def show_scan_start(target: str, plugin_name: str, verbosity: int = 0, start_time: Optional[float] = None):
        """Update TUI with scan start"""
        global scan_ui
        if scan_ui:
            # If start_time is provided, update the tool's start time
            if start_time and plugin_name in scan_ui.tools:
                scan_ui.tools[plugin_name].start_time = start_time
            scan_ui.start_tool(plugin_name)
        else:
            # Fallback: start UI if not started yet
            start_scan_ui(target)
            if scan_ui:
                if start_time and plugin_name in scan_ui.tools:
                    scan_ui.tools[plugin_name].start_time = start_time
                scan_ui.start_tool(plugin_name)

    @staticmethod
    def show_scan_completion(target: str, plugin_name: str, duration: str, success: bool = True, verbosity: int = 0, end_time: Optional[float] = None):
        """Update TUI with scan completion"""
        global scan_ui
        if scan_ui:
            # If end_time is provided, set it before completion
            if end_time and plugin_name in scan_ui.tools:
                scan_ui.tools[plugin_name].end_time = end_time

            if success:
                scan_ui.complete_tool(plugin_name, f"Completed in {duration}")
            else:
                scan_ui.error_tool(plugin_name, "Failed")
    
    @staticmethod
    def show_service_discovery(target: str, service_name: str, protocol: str, port: int, verbosity: int = 0):
        """Show discovered service in TUI"""
        global scan_ui
        if scan_ui:
            tool_name = f"Discovered: {service_name} on {protocol}/{port}"
            scan_ui.add_tool(tool_name)
            scan_ui.complete_tool(tool_name)

    @staticmethod
    def show_command_execution(target: str, plugin_name: str, command: str, verbosity: int = 0):
        """Update TUI with wordlist information from command if available"""
        global scan_ui
        if scan_ui:
            # Try to extract wordlist info from command and update the tool
            wordlist_info = extract_wordlist_from_command(command)
            if wordlist_info and plugin_name in scan_ui.tools:
                # Update the existing tool with wordlist information
                tool_info = scan_ui.tools[plugin_name]
                if not tool_info.wordlist_info:  # Only update if not already set
                    tool_info.wordlist_info = wordlist_info
                    tool_info.name = f"{plugin_name} using {wordlist_info}"
                    scan_ui.active_wordlists[plugin_name] = wordlist_info

    @staticmethod
    def show_scan_result(target: str, plugin_name: str, result: str, level: str = "info", verbosity: int = 0):
        """Silent scan results - no output to prevent TUI stuttering"""
        pass

    @staticmethod
    def show_pattern_match(target: str, plugin_name: str, pattern: str, match: str, verbosity: int = 0):
        """Silent pattern matches - no output to prevent TUI stuttering"""
        pass

    @staticmethod
    def show_command_output(target: str, plugin_name: str, line: str, verbosity: int = 0):
        """Silent command output - no output to prevent TUI stuttering"""
        pass

    @staticmethod
    def show_progress_summary(active_scans: list, verbosity: int = 0, preserve_nmap_timing: bool = True):
        """Silent progress summary - no output to prevent TUI stuttering"""
        pass

    @staticmethod
    def show_verbosity_guide():
        """Silent verbosity guide - no output to prevent TUI stuttering"""
        pass

def show_completion_message(target: Optional[str] = None, duration: str = "00:00:00", completed_tools: int = 0, error_tools: int = 0, canceled: bool = False, timed_out: bool = False):
    """Show the standardized completion message for scan completion or cancellation"""
    console = Console()

    # Determine the status and title
    if canceled:
        status_text = "🛑 Scan Canceled"
        title = "🕷️ Canceled"
        border_style = "yellow"
    elif timed_out:
        status_text = "⏰ Scan Timed Out"
        title = "🕷️ Timeout"
        border_style = "red"
    else:
        status_text = "✅ Scan Completed"
        title = "🕷️ Success"
        border_style = "green"

    # Build the content
    content = Text(f"\n{status_text}\n\n", style="bold", justify="center")

    if target:
        content += Text(f"Target: {target}\n", style="cyan")

    content += Text(f"Duration: {duration}\n", style="white")

    if completed_tools > 0 or error_tools > 0:
        content += Text(f"Tools completed: {completed_tools}\n", style="green")
        content += Text(f"Tools with errors: {error_tools}\n\n", style="red" if error_tools > 0 else "green")
    else:
        content += Text("\n")

    content += Text("🎉 Thank you for using ipcrawler!\n", style="cyan bold", justify="center")
    content += Text("Results are available in results/ folder\n", style="dim white", justify="center")

    console.print()
    console.print(Panel.fit(
        content,
        title=title,
        border_style=border_style,
        box=box.ROUNDED
    ))

# Global status instance
scan_status = ScanStatus()