#!/usr/bin/env python3
"""
Final test of the new TUI layout with persistent wordlists
"""

import asyncio
import time
from ipcrawler.loading import ScanUI

async def test_final_tui():
    """Test the final TUI with the new layout: Left (Active), Middle (Completed), Right (Wordlists)"""
    print("🧪 Testing final TUI layout...")
    
    # Create UI for test target
    ui = ScanUI('permx.htb')
    ui.start_ui()
    
    try:
        print("📚 Phase 1: Starting tools with wordlists...")
        
        # Start multiple tools with different wordlists
        ui.start_tool('Feroxbuster', 'common.txt (2,048 lines)')
        await asyncio.sleep(1)
        
        ui.start_tool('Gobuster', 'directory-list-2.3-medium.txt (220,560 lines)')
        await asyncio.sleep(1)
        
        ui.start_tool('FFuf', 'raft-medium-directories.txt (30,000 lines)')
        await asyncio.sleep(1)
        
        ui.start_tool('Wfuzz', 'subdomains-top1million-5000.txt (4,989 lines)')
        await asyncio.sleep(2)
        
        print("✅ Phase 1: All tools started with wordlists")
        
        print("📊 Phase 2: Completing some tools...")
        
        # Complete tools one by one to show progression
        ui.complete_tool('Feroxbuster', 'Found 15 directories')
        await asyncio.sleep(2)
        
        ui.complete_tool('Gobuster', 'Found 42 directories')
        await asyncio.sleep(2)
        
        # Error one tool
        ui.error_tool('FFuf', 'Connection timeout')
        await asyncio.sleep(2)
        
        print("📚 Phase 3: Adding more tools to test wordlist panel...")
        
        # Add more tools to test the wordlist panel
        ui.start_tool('Dirsearch', 'big.txt (20,469 lines)')
        await asyncio.sleep(1)
        
        ui.start_tool('Dirb', 'small.txt (959 lines)')
        await asyncio.sleep(2)
        
        print("🏁 Phase 4: Completing remaining tools...")
        
        ui.complete_tool('Wfuzz', 'Found 3 subdomains')
        await asyncio.sleep(1)
        
        ui.complete_tool('Dirsearch', 'Found 8 files')
        await asyncio.sleep(1)
        
        ui.complete_tool('Dirb', 'Found 2 files')
        await asyncio.sleep(3)
        
        print("✅ All phases completed!")
        print("📋 Layout verification:")
        print("   Left panel: Active scans (should show running tools)")
        print("   Middle panel: Completed scans (should show all completed tools)")
        print("   Right panel: Wordlists (should show active + history)")
        print("   Header: Should show wordlist info in timer section")
        
    finally:
        ui.stop_ui()

if __name__ == "__main__":
    asyncio.run(test_final_tui())
