#!/usr/bin/env python3
"""
Test script for session-only persistence (no cross-scan persistence)
"""

import asyncio
import time
from ipcrawler.loading import Scan<PERSON>, stop_scan_ui, start_scan_ui

async def test_session_persistence():
    """Test that persistence works within a scan session but not across different scans"""
    print("🧪 Testing session-only persistence...")
    
    print("\n📝 Scan 1: First target...")
    
    # First scan session
    ui1 = ScanUI('target1.htb')
    ui1.start_ui()
    
    try:
        # Add tools with wordlists
        ui1.start_tool('Feroxbuster', 'common.txt')
        await asyncio.sleep(0.5)
        
        ui1.start_tool('Gobuster', 'directory-list.txt')
        await asyncio.sleep(0.5)
        
        # Complete tools
        ui1.complete_tool('Feroxbuster')  # No summary - should show only tool name
        await asyncio.sleep(0.5)
        
        ui1.complete_tool('Gobuster')  # No summary - should show only tool name
        await asyncio.sleep(1)
        
        print(f"✅ Scan 1: {len(ui1.session_data.completed_tools)} completed tools")
        
    finally:
        ui1.stop_ui(show_completion=False)
    
    print("\n📝 Scan 2: Different target (should start fresh)...")
    
    # Second scan session - different target, should start fresh
    ui2 = ScanUI('target2.htb')
    ui2.start_ui()
    
    try:
        # Should start with empty completed tools
        print(f"📊 Scan 2 initial state: {len(ui2.session_data.completed_tools)} completed tools (should be 0)")
        
        # Add new tools
        ui2.start_tool('Wfuzz', 'subdomains.txt')
        await asyncio.sleep(0.5)
        
        ui2.start_tool('Dirsearch', 'big.txt')
        await asyncio.sleep(0.5)
        
        # Complete tools
        ui2.complete_tool('Wfuzz')  # No summary
        await asyncio.sleep(0.5)
        
        ui2.complete_tool('Dirsearch')  # No summary
        await asyncio.sleep(1)
        
        print(f"✅ Scan 2: {len(ui2.session_data.completed_tools)} completed tools")
        
    finally:
        ui2.stop_ui(show_completion=False)
    
    print("\n📝 Scan 3: Same target as Scan 1 (should still start fresh)...")
    
    # Third scan session - same target as first, should still start fresh
    ui3 = ScanUI('target1.htb')
    ui3.start_ui()
    
    try:
        # Should start with empty completed tools even though same target
        print(f"📊 Scan 3 initial state: {len(ui3.session_data.completed_tools)} completed tools (should be 0)")
        
        # Add tools to verify it works
        ui3.start_tool('Nmap', 'N/A')
        await asyncio.sleep(0.5)
        
        ui3.complete_tool('Nmap')
        await asyncio.sleep(1)
        
        print(f"✅ Scan 3: {len(ui3.session_data.completed_tools)} completed tools")
        
    finally:
        ui3.stop_ui(show_completion=False)
    
    print("\n🎉 Session persistence test completed!")
    print("✅ Each scan session starts fresh (no cross-scan persistence)")
    print("✅ Completed scans show only tool names (no discovered output)")

async def test_within_session_persistence():
    """Test that data persists within a single scan session"""
    print("\n🧪 Testing within-session persistence...")
    
    ui = ScanUI('session-test.htb')
    ui.start_ui()
    
    try:
        # Phase 1: Add some tools
        print("📝 Phase 1: Adding tools...")
        ui.start_tool('Tool1', 'wordlist1.txt')
        ui.start_tool('Tool2', 'wordlist2.txt')
        await asyncio.sleep(0.5)
        
        # Phase 2: Complete some tools
        print("📝 Phase 2: Completing tools...")
        ui.complete_tool('Tool1')
        await asyncio.sleep(0.5)
        
        # Phase 3: Add more tools while others are completed
        print("📝 Phase 3: Adding more tools...")
        ui.start_tool('Tool3', 'wordlist3.txt')
        await asyncio.sleep(0.5)
        
        # Phase 4: Complete remaining tools
        print("📝 Phase 4: Completing remaining tools...")
        ui.complete_tool('Tool2')
        ui.complete_tool('Tool3')
        await asyncio.sleep(1)
        
        # Verify persistence within session
        print(f"📊 Final state: {len(ui.session_data.completed_tools)} completed tools")
        print(f"📚 Wordlist history: {len(ui.session_data.wordlist_history)} entries")
        
        # All completed tools should be visible
        assert len(ui.session_data.completed_tools) == 3, "All completed tools should be visible"
        
        print("✅ Within-session persistence works correctly!")
        
    finally:
        ui.stop_ui(show_completion=False)

async def main():
    await test_session_persistence()
    await test_within_session_persistence()

if __name__ == "__main__":
    asyncio.run(main())
