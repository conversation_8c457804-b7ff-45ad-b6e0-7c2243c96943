#!/usr/bin/env python3
"""
Test the clean TUI layout with session-only persistence
"""

import asyncio
import time
from ipcrawler.loading import ScanUI

async def test_clean_tui():
    """Test the clean TUI layout"""
    print("🧪 Testing clean TUI layout...")
    
    # Create UI for test target
    ui = ScanUI('clean-test.htb')
    ui.start_ui()
    
    try:
        print("📚 Phase 1: Starting tools with wordlists...")
        
        # Start multiple tools with different wordlists
        ui.start_tool('Feroxbuster', 'common.txt (2,048 lines)')
        await asyncio.sleep(1)
        
        ui.start_tool('Gobuster', 'directory-list-2.3-medium.txt (220,560 lines)')
        await asyncio.sleep(1)
        
        ui.start_tool('FFuf', 'raft-medium-directories.txt (30,000 lines)')
        await asyncio.sleep(2)
        
        print("✅ Phase 1: Tools started - check wordlists in right panel and timer")
        
        print("📊 Phase 2: Completing tools (clean display - no discovered output)...")
        
        # Complete tools - should show ONLY tool names, no discovered output
        ui.complete_tool('Feroxbuster')  # No summary = clean display
        await asyncio.sleep(2)
        
        ui.complete_tool('Gobuster')  # No summary = clean display
        await asyncio.sleep(2)
        
        print("📚 Phase 3: Adding more tools...")
        
        # Add more tools to test the wordlist panel
        ui.start_tool('Wfuzz', 'subdomains-top1million-5000.txt (4,989 lines)')
        await asyncio.sleep(1)
        
        ui.start_tool('Dirsearch', 'big.txt (20,469 lines)')
        await asyncio.sleep(2)
        
        print("🏁 Phase 4: Completing remaining tools...")
        
        # Error one tool
        ui.error_tool('FFuf', 'Connection timeout')
        await asyncio.sleep(1)
        
        ui.complete_tool('Wfuzz')  # Clean display
        await asyncio.sleep(1)
        
        ui.complete_tool('Dirsearch')  # Clean display
        await asyncio.sleep(3)
        
        print("✅ All phases completed!")
        print("📋 Layout verification:")
        print("   Left panel: Active scans (should be empty now)")
        print("   Middle panel: Completed scans (ONLY tool names, no discovered output)")
        print("   Right panel: Wordlists (active + history from this session)")
        print("   Header: Should show wordlist info when tools were active")
        
    finally:
        ui.stop_ui()

if __name__ == "__main__":
    asyncio.run(test_clean_tui())
