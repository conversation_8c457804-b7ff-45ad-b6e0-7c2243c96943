#!/usr/bin/env python3
"""
Test script for the new wordlist TUI features
"""

import asyncio
import time
from ipcrawler.loading import ScanUI

async def test_wordlist_tui():
    """Test the new TUI with wordlist display"""
    print("🧪 Testing new wordlist TUI features...")
    
    # Create UI for test target
    ui = ScanUI('permx.htb')
    ui.start_ui()
    
    try:
        # Test 1: Start tools with different wordlists
        print("📚 Testing wordlist display...")
        ui.start_tool('Feroxbuster', 'common.txt (2,048 lines)')
        await asyncio.sleep(1)
        
        ui.start_tool('Gobuster', 'directory-list-2.3-medium.txt (220,560 lines)')
        await asyncio.sleep(1)
        
        ui.start_tool('FFuf', 'raft-medium-directories.txt (30,000 lines)')
        await asyncio.sleep(2)
        
        # Test 2: Complete some tools
        print("✅ Testing completed scans...")
        ui.complete_tool('Feroxbuster', 'Found 15 directories')
        await asyncio.sleep(1)
        
        # Test 3: Error one tool
        print("❌ Testing error handling...")
        ui.error_tool('FFuf', 'Connection timeout')
        await asyncio.sleep(1)
        
        # Test 4: Add more tools to test multiple wordlists
        print("📚 Testing multiple wordlists...")
        ui.start_tool('Wfuzz', 'subdomains-top1million-5000.txt (4,989 lines)')
        await asyncio.sleep(1)
        
        ui.start_tool('Dirsearch', 'big.txt (20,469 lines)')
        await asyncio.sleep(2)
        
        # Test 5: Complete remaining tools
        print("🏁 Completing remaining scans...")
        ui.complete_tool('Gobuster', 'Found 42 directories')
        await asyncio.sleep(1)
        
        ui.complete_tool('Wfuzz', 'Found 3 subdomains')
        await asyncio.sleep(1)
        
        ui.complete_tool('Dirsearch', 'Found 8 files')
        await asyncio.sleep(2)
        
        print("✅ Test completed successfully!")
        
    finally:
        ui.stop_ui()

if __name__ == "__main__":
    asyncio.run(test_wordlist_tui())
